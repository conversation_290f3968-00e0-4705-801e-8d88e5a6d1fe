// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _unmannedtrader_close_item_inform_zocl
    {
        char byTaxRate;
        unsigned __int16 wItemSerial;
        unsigned int dwRegistSerial;
        unsigned int dwPrice;
    public:
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_unmannedtrader_close_item_inform_zocl, 11>(), "_unmannedtrader_close_item_inform_zocl");
END_ATF_NAMESPACE
