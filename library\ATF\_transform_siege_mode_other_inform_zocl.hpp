// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _transform_siege_mode_other_inform_zocl
    {
        unsigned int dwAvatorSerial;
        unsigned __int16 wWeaponItemIndex;
        char bySiegeItemIndex;
        unsigned __int16 wVisualVer;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE
