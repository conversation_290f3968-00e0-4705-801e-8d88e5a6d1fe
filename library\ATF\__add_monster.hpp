// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_react_area.hpp>
#include <_react_obj.hpp>


START_ATF_NAMESPACE
    struct __add_monster
    {
        _react_obj ReactObj;
        _react_area ReactArea;
    public:
        __add_monster();
        void ctor___add_monster();
    };
END_ATF_NAMESPACE
