// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _TRAP_PARAM
    {
        struct _param
        {
            struct CTrap *pItem;
            unsigned int dwSerial;
        public:
            _param();
            void ctor__param();
            void init();
            bool isLoad();
        };
        _param m_Item[20];
        int m_nCount;
    public:
        int GetNum();
        void Init();
        bool PopItem(unsigned int dwTrapSerial);
        bool PushItem(struct CTrap* pTrap, unsigned int dwTrapSerial);
        _TRAP_PARAM();
        void ctor__TRAP_PARAM();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_TRAP_PARAM, 328>(), "_TRAP_PARAM");
END_ATF_NAMESPACE
