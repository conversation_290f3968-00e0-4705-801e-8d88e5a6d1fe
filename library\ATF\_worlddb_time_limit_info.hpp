// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _worlddb_time_limit_info
    {
        unsigned int dwAccSerial;
        unsigned int dwFatigue;
        char byTLStatus;
        unsigned int dwLastLogoutTime;
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_worlddb_time_limit_info, 16>(), "_worlddb_time_limit_info");
END_ATF_NAMESPACE
