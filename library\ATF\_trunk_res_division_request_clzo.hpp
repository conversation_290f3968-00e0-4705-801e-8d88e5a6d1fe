// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _trunk_res_division_request_clzo
    {
        unsigned __int16 wStartSerial;
        unsigned __int16 wTarSerial;
        unsigned __int16 wMoveAmount;
        char byStorageIndex;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE
