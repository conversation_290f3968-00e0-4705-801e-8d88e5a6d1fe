// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_worlddb_arrange_char_info.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _worlddb_arrange_char_infoctor__worlddb_arrange_char_info2_ptr = void (WINAPIV*)(struct _worlddb_arrange_char_info*);
        using _worlddb_arrange_char_infoctor__worlddb_arrange_char_info2_clbk = void (WINAPIV*)(struct _worlddb_arrange_char_info*, _worlddb_arrange_char_infoctor__worlddb_arrange_char_info2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE
