// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _worlddb_update_char_query
    {
        char *szBaseQuery;
        char *szGeneralQuery;
        char *szInvenQuery;
        char *szUnitQuery;
        char *szUIQuery;
        char *szQuestQuery;
        char *szItemCombineExQuery;
        char *wszBuddyQuery;
        char *wszTrunkQuery;
        char *szAMPInvenQuery;
        char *szPvpPointLimitQuery;
        char *wszBossCryMsgQuery;
        char *szPvpOrderViewQurey;
        char *szNPCQuestQuery;
        char *szPcBangPlayTimeQuery;
        char *szSupplementQuery;
        char *szPotionDelayQuery;
        char *szOreCuttingQuery;
        char *szPcBangFavorQuery;
        char *wszExtTrunkQuery;
        char *szTimeLimitInfoQuery;
    };    
    static_assert(ATF::checkSize<_worlddb_update_char_query, 168>(), "_worlddb_update_char_query");
END_ATF_NAMESPACE
