// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _TOOL_COL_LINE
    {
        unsigned int attr;
        unsigned __int16 start_v;
        unsigned __int16 end_v;
        float height;
        unsigned __int16 front;
        unsigned __int16 back;
    };
END_ATF_NAMESPACE
