// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _worlddb_npc_quest_complete_history
    {
        struct __list
        {
            char szQuestCode[8];
            char byLevel;
            unsigned int dwEventEndTime;
        public:
            __list();
            void ctor___list();
        };
        __list List[70];
    public:
        _worlddb_npc_quest_complete_history();
        void ctor__worlddb_npc_quest_complete_history();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_worlddb_npc_quest_complete_history, 1120>(), "_worlddb_npc_quest_complete_history");
END_ATF_NAMESPACE
