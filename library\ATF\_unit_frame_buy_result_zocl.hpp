// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _unit_frame_buy_result_zocl
    {
        char byRetCode;
        char byFrameCode;
        char byAddUnitSlot;
        char byKeyIndex;
        unsigned __int16 wKeySerial;
        unsigned int dwLeftMoney[7];
        unsigned int dwConsumMoney[7];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE
