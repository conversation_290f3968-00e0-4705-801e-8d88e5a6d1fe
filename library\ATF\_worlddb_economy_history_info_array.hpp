// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _worlddb_economy_history_info_array
    {
        struct _worlddb_economy_history_info
        {
            long double dTradeGold[3];
            long double dTradeDalant[3];
            unsigned int dwManageValue;
            long double dMineOre[3][3];
            long double dCutOre[3][3];
        };
        unsigned __int16 wRowCount;
        _worlddb_economy_history_info EconomyData[12];
    };
END_ATF_NAMESPACE
