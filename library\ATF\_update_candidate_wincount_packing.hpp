// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct _update_candidate_wincount_packing
    {
        char byRace;
        unsigned int dwWinCnt;
        char wszdName[17];
    public:
        _update_candidate_wincount_packing();
        void ctor__update_candidate_wincount_packing();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_update_candidate_wincount_packing, 28>(), "_update_candidate_wincount_packing");
END_ATF_NAMESPACE
