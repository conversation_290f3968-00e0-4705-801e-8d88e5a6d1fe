// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <__guild_list_page.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using __guild_list_pagector___guild_list_page2_ptr = void (WINAPIV*)(struct __guild_list_page*);
        using __guild_list_pagector___guild_list_page2_clbk = void (WINAPIV*)(struct __guild_list_page*, __guild_list_pagector___guild_list_page2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE
