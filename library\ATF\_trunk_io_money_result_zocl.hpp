// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _trunk_io_money_result_zocl
    {
        char byRetCode;
        long double dTrunkDalant;
        long double dTrunkGold;
        unsigned int dwCharDalant;
        unsigned int dwCharGold;
        unsigned int dwFeeDalant;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE
