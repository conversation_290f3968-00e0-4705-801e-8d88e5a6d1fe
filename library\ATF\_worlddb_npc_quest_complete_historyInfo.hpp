// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_worlddb_npc_quest_complete_history.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _worlddb_npc_quest_complete_historyctor__worlddb_npc_quest_complete_history2_ptr = void (WINAPIV*)(struct _worlddb_npc_quest_complete_history*);
        using _worlddb_npc_quest_complete_historyctor__worlddb_npc_quest_complete_history2_clbk = void (WINAPIV*)(struct _worlddb_npc_quest_complete_history*, _worlddb_npc_quest_complete_historyctor__worlddb_npc_quest_complete_history2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE
