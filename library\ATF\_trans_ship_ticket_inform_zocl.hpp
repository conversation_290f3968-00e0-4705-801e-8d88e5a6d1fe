// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _trans_ship_ticket_inform_zocl
    {
        struct  __list
        {
            char byDirectCode;
            unsigned __int16 wLeftTicketNum;
        };
        unsigned int dwNpcSerial;
        __list TicketList[2];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE
