// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _worlddb_character_array_info
    {
        struct __worlddb_charater_info
        {
            unsigned __int16 wWorldCode;
            char wszAvatorName[17];
            char bySlotIndex;
            char byRaceCode;
            char byLevel;
            unsigned int dwSerial;
            char byDck;
        };
        unsigned __int16 wCharacterCount;
        __worlddb_charater_info CharacterInfo[200];
    };
END_ATF_NAMESPACE
