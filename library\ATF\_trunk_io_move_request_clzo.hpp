// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _trunk_io_move_request_clzo
    {
        char byStartStorageIndex;
        char byTarStorageIndex;
        unsigned __int16 wItemSerial;
        char byClientSlotIndex;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE
