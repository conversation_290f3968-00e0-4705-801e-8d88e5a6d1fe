// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_STORAGE_LIST.hpp>


START_ATF_NAMESPACE
    struct  _trunk_db_load : _STORAGE_LIST
    {
        char m_byItemSlotRace[100];
        _STORAGE_LIST::_db_con m_List[100];
    public:
        _trunk_db_load();
        void ctor__trunk_db_load();
    };    
    static_assert(ATF::checkSize<_trunk_db_load, 5120>(), "_trunk_db_load");
END_ATF_NAMESPACE
