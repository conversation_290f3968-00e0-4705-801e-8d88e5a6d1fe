// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _unmannedtrader_buy_item_info
    {
        char byInveninx;
        unsigned int dwK;
        unsigned __int64 dwD;
        unsigned int dwU;
        unsigned int dwPrice;
        unsigned int dwSeller;
        unsigned int dwT;
        unsigned __int64 lnUID;
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_unmannedtrader_buy_item_info, 40>(), "_unmannedtrader_buy_item_info");
END_ATF_NAMESPACE
