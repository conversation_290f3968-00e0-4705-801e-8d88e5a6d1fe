// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _unmannedtrader_re_regist_result_zocl
    {
        struct __list
        {
            char byRet;
            bool bRegist;
            unsigned __int16 wItemSerial;
            unsigned int dwPrice;
            unsigned int dwRegedSerial;
            unsigned int dwListIndex;
            unsigned int dwTax;
        };
        unsigned int dwLeftDalant;
        char byNum;
        __list List[10];
    public:
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_unmannedtrader_re_regist_result_zocl, 205>(), "_unmannedtrader_re_regist_result_zocl");
END_ATF_NAMESPACE
