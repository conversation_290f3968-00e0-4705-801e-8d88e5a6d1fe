// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>



START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _wfinddata64i32_t
    {
        unsigned int attrib;
        __int64 time_create;
        __int64 time_access;
        __int64 time_write;
        unsigned int size;
        wchar_t name[260];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE
