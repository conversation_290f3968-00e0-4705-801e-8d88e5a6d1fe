// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _unmannedtrader_reserved_schedule_info
    {
        struct __list
        {
            char byType;
            unsigned int dwItemSerial;
            __int64 tEndTime;
            unsigned int dwOwnerSerial;
            unsigned int dwK;
        };
        unsigned int dwCnt;
        __list list[10];
    };
END_ATF_NAMESPACE
