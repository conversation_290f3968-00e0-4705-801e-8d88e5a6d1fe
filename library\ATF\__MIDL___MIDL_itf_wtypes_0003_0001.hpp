// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_GUID.hpp>
#include <$F7FDFB5D8FA764F7B3C893599610F237.hpp>
#include <$251690E993DCC025BA8F9E6117C2F012.hpp>


START_ATF_NAMESPACE
    struct __MIDL___MIDL_itf_wtypes_0003_0001
    {
        union __MIDL___MIDL_itf_wtypes_0003_0005
        {
            _GUID clsid;
            wchar_t *pFileExt;
            wchar_t *pMimeType;
            wchar_t *pProgId;
            wchar_t *pFileName;
            $F7FDFB5D8FA764F7B3C893599610F237 ByName;
            $251690E993DCC025BA8F9E6117C2F012 ByObjectId;
        };
        unsigned int tyspec;
        __MIDL___MIDL_itf_wtypes_0003_0005 tagged_union;
    };
END_ATF_NAMESPACE
