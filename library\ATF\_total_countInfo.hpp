// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_total_count.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _total_countctor__total_count2_ptr = void (WINAPIV*)(struct _total_count*);
        using _total_countctor__total_count2_clbk = void (WINAPIV*)(struct _total_count*, _total_countctor__total_count2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE
