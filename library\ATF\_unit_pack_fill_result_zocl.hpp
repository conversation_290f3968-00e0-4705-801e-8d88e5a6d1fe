// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_unit_pack_fill_request_clzo.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _unit_pack_fill_result_zocl
    {
        char byRetCode;
        char bySlotIndex;
        unsigned int dwComsumMoney[7];
        unsigned int dwLeftMoney[7];
        char by<PERSON><PERSON><PERSON><PERSON>;
        _unit_pack_fill_request_clzo::__list List[8];
    public:
        _unit_pack_fill_result_zocl();
        void ctor__unit_pack_fill_result_zocl();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_unit_pack_fill_result_zocl, 83>(), "_unit_pack_fill_result_zocl");
END_ATF_NAMESPACE
