// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <__holy_stone_data.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using __holy_stone_datactor___holy_stone_data2_ptr = void (WINAPIV*)(struct __holy_stone_data*);
        using __holy_stone_datactor___holy_stone_data2_clbk = void (WINAPIV*)(struct __holy_stone_data*, __holy_stone_datactor___holy_stone_data2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE
