// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>



START_ATF_NAMESPACE
    struct _TRUSTEE_W
    {
        _TRUSTEE_W *pMultipleTrustee;
        _MULTIPLE_TRUSTEE_OPERATION MultipleTrusteeOperation;
        _TRUSTEE_FORM TrusteeForm;
        _TRUSTEE_TYPE TrusteeType;
        wchar_t *ptstrName;
    };
END_ATF_NAMESPACE
