// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace vc_attributes
    {
        template<>
        struct moduleAttribute
        {
            template<>
            enum type_e
            {
                dll = 0x1,
                exe = 0x2,
                service = 0x3,
                unspecified = 0x4,
                EXE = 0x2,
                SERVICE = 0x3,
            };
            type_e type;
            const char *name;
            const char *version;
            const char *uuid;
            int lcid;
            bool control;
            const char *helpstring;
            int helpstringcontext;
            const char *helpstringdll;
            const char *helpfile;
            int helpcontext;
            bool hidden;
            bool restricted;
            const char *custom;
            const char *resource_name;
        };
    }; // end namespace vc_attributes
END_ATF_NAMESPACE
