// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_TRAP_PARAM.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _TRAP_PARAMGetNum2_ptr = int (WINAPIV*)(struct _TRAP_PARAM*);
        using _TRAP_PARAMGetNum2_clbk = int (WINAPIV*)(struct _TRAP_PARAM*, _TRAP_PARAMGetNum2_ptr);
        using _TRAP_PARAMInit4_ptr = void (WINAPIV*)(struct _TRAP_PARAM*);
        using _TRAP_PARAMInit4_clbk = void (WINAPIV*)(struct _TRAP_PARAM*, _TRAP_PARAMInit4_ptr);
        using _TRAP_PARAMPopItem6_ptr = bool (WINAPIV*)(struct _TRAP_PARAM*, unsigned int);
        using _TRAP_PARAMPopItem6_clbk = bool (WINAPIV*)(struct _TRAP_PARAM*, unsigned int, _TRAP_PARAMPopItem6_ptr);
        using _TRAP_PARAMPushItem8_ptr = bool (WINAPIV*)(struct _TRAP_PARAM*, struct CTrap*, unsigned int);
        using _TRAP_PARAMPushItem8_clbk = bool (WINAPIV*)(struct _TRAP_PARAM*, struct CTrap*, unsigned int, _TRAP_PARAMPushItem8_ptr);
        
        using _TRAP_PARAMctor__TRAP_PARAM10_ptr = void (WINAPIV*)(struct _TRAP_PARAM*);
        using _TRAP_PARAMctor__TRAP_PARAM10_clbk = void (WINAPIV*)(struct _TRAP_PARAM*, _TRAP_PARAMctor__TRAP_PARAM10_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE
