// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _worlddb_character_base_info
    {
        char wszName[17];
        unsigned int dwSerial;
        char byRace;
        char szClassCode[5];
        char bySlotIndex;
        char byLevel;
        unsigned int dwDalant;
        unsigned int dwGold;
        unsigned int dwBaseShape;
        unsigned int dwLastConnTime;
        char szAccount[17];
        __int16 shEKArray[8];
        unsigned int dwEUArray[8];
        unsigned int dwETArray[8];
        unsigned __int64 lnUIDArray[8];
        unsigned int dwCheckSum;
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_worlddb_character_base_info, 224>(), "_worlddb_character_base_info");
END_ATF_NAMESPACE
