// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace vc_attributes
    {
        namespace atl
        {
            #pragma pack(push, 8)
            template<>
            struct perf_objectAttribute
            {
                int name_res;
                int help_res;
                const char *namestring;
                const char *helpstring;
                int detail;
                bool no_instances;
                const char *class_;
                int maxinstnamelen;
            };
            #pragma pack(pop)
        }; // end namespace atl
    }; // end namespace vc_attributes
END_ATF_NAMESPACE
