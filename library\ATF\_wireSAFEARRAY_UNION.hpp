// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _wireSAFEARRAY_UNION
    {
        union __MIDL_IOleAutomationTypes_0001
        {
            _wireSAFEARR_BSTR BstrStr;
            _wireSAFEARR_UNKNOWN UnknownStr;
            _wireSAFEARR_DISPATCH DispatchStr;
            _wireSAFEARR_VARIANT VariantStr;
            _wireSAFEARR_BRECORD RecordStr;
            _wireSAFEARR_HAVEIID HaveIidStr;
            _BYTE_SIZEDARR ByteStr;
            _SHORT_SIZEDARR WordStr;
            _LONG_SIZEDARR LongStr;
            _HYPER_SIZEDARR HyperStr;
        };
        unsigned int sfType;
        __MIDL_IOleAutomationTypes_0001 u;
    };
END_ATF_NAMESPACE
