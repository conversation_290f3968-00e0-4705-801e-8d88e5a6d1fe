// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _worlddb_inven_info
    {
        struct __inven_key
        {
            int lK;
            unsigned __int64 dwD;
            unsigned int dwU;
            unsigned int dwT;
            unsigned __int64 lnUID;
        };
        __inven_key invenKey[100];
    };
END_ATF_NAMESPACE
