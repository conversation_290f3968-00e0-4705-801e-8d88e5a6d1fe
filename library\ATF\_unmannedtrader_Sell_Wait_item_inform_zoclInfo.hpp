// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_unmannedtrader_Sell_Wait_item_inform_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _unmannedtrader_Sell_Wait_item_inform_zoclctor__unmannedtrader_Sell_Wait_item_inform_zocl2_ptr = void (WINAPIV*)(struct _unmannedtrader_Sell_Wait_item_inform_zocl*);
        using _unmannedtrader_Sell_Wait_item_inform_zoclctor__unmannedtrader_Sell_Wait_item_inform_zocl2_clbk = void (WINAPIV*)(struct _unmannedtrader_Sell_Wait_item_inform_zocl*, _unmannedtrader_Sell_Wait_item_inform_zoclctor__unmannedtrader_Sell_Wait_item_inform_zocl2_ptr);
        using _unmannedtrader_Sell_Wait_item_inform_zoclsize4_ptr = int (WINAPIV*)(struct _unmannedtrader_Sell_Wait_item_inform_zocl*);
        using _unmannedtrader_Sell_Wait_item_inform_zoclsize4_clbk = int (WINAPIV*)(struct _unmannedtrader_Sell_Wait_item_inform_zocl*, _unmannedtrader_Sell_Wait_item_inform_zoclsize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE
