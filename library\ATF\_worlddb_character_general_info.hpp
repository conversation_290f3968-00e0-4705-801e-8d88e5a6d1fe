// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _worlddb_character_general_info
    {
        unsigned int dwHP;
        unsigned int dwFP;
        unsigned int dwSP;
        unsigned int dwDP;
        long double dExp;
        long double dLoseExp;
        char byBagNum;
        char byMapCode;
        float fStartPos[3];
        unsigned int dwTotalPlayMin;
        char szLeftResList[160];
        int lEK[7];
        unsigned __int16 wED[7];
        unsigned int dwET[7];
        unsigned __int64 lnUID_E[7];
        int lF[88];
        unsigned __int64 lnUID_F[88];
        unsigned int dwWM[2];
        unsigned int dwFM[24];
        unsigned int dwSM[48];
        unsigned int dwMI[3];
        unsigned int dwSR;
        unsigned int dwDM;
        unsigned int dwPM;
        char byAK[4];
        unsigned __int64 dwAD[4];
        unsigned int dwAP[4];
        unsigned __int64 lnUID_A[88];
        __int16 zClassHistory[3];
        unsigned int dwClassInitCnt;
        char byLastClassGrade;
        long double dPvPPoint;
        long double dPvPCashBag;
        char szBindMapCode[12];
        char szBindDummy[12];
        unsigned int dwGuildSerial;
        char byGuildGrade;
        unsigned int dwRadarDelayTime;
        unsigned int dwTakeLastMentalTicket;
        unsigned int dwTakeLastCriTicket;
        char byMaxLevel;
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_worlddb_character_general_info, 2560>(), "_worlddb_character_general_info");
END_ATF_NAMESPACE
