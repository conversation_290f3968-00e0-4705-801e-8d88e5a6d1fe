// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_UNWIND_HISTORY_TABLE_ENTRY.hpp>


START_ATF_NAMESPACE
    struct _UNWIND_HISTORY_TABLE
    {
        unsigned int Count;
        char Search;
        unsigned __int64 LowAddress;
        unsigned __int64 HighAddress;
        _UNWIND_HISTORY_TABLE_ENTRY Entry[12];
    };
END_ATF_NAMESPACE
