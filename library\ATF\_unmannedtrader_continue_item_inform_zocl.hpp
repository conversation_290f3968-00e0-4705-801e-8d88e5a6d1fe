// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _unmannedtrader_continue_item_inform_zocl
    {
        struct  __list
        {
            unsigned __int16 wItemSerial;
            unsigned int dwRegistSerial;
            unsigned int dwPrice;
        };
        char byTaxRate;
        char byNum;
        __list List[10];
    public:
        int size();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE
