// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _trunk_potionsocket_division_result_zocl
    {
        char sErrorCode;
        unsigned __int16 wSerial;
        unsigned __int16 wParentAmount;
        unsigned __int16 wChildSerial;
        unsigned __int16 wChildAmount;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE
