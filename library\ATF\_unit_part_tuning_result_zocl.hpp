// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _unit_part_tuning_result_zocl
    {
        char byRetCode;
        char bySlotIndex;
        char byPart[6];
        unsigned int dwBullet[2];
        int nCost[7];
        unsigned int dwLeftMoney[7];
    };
END_ATF_NAMESPACE
