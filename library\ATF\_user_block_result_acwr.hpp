// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_CLID.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _user_block_result_acwr
    {
        char byBlockResult;
        _CLID idLocalForTarget;
        _CLID idLocalForGM;
        unsigned int dwAccountSerial;
        int bLogin;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE
