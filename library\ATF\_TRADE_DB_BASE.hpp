// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _TRADE_DB_BASE
    {
        struct  _LIST
        {
            char byState;
            unsigned int dwRegistSerial;
            char byInvenIndex;
            unsigned int dwPrice;
            __int64 tStartTime;
            char bySellTurm;
            unsigned int dwBuyerSerial;
            unsigned int dwTax;
            __int64 tResultTime;
            char wszBuyerName[17];
            char szBuyerAccount[13];
        public:
            void Clear();
            bool IsEmpty();
            _LIST();
            void ctor__LIST();
        };
        _LIST m_List[20];
    public:
        void Clear();
        void Init();
        _TRADE_DB_BASE();
        void ctor__TRADE_DB_BASE();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_TRADE_DB_BASE, 1300>(), "_TRADE_DB_BASE");
END_ATF_NAMESPACE
