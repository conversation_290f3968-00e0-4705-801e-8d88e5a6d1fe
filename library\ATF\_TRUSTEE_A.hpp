// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _TRUSTEE_A
    {
        _TRUSTEE_A *pMultipleTrustee;
        _MULTIPLE_TRUSTEE_OPERATION MultipleTrusteeOperation;
        _TRUSTEE_FORM TrusteeForm;
        _TRUSTEE_TYPE TrusteeType;
        char *ptstrName;
    };
END_ATF_NAMESPACE
