// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _worlddb_character_supplement_info
    {
        long double dPvpPointLeak;
        bool bLastAttBuff;
        unsigned int dwBufPotionEndTime;
        unsigned int dwRaceBuffClear;
        char byVoted;
        char VoteEnable;
        unsigned __int64 dwScanerCnt;
        unsigned int dwAccumPlayTime;
        unsigned int dwLastResetDate;
        unsigned int dwActionPoint[3];
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_worlddb_character_supplement_info, 56>(), "_worlddb_character_supplement_info");
END_ATF_NAMESPACE
