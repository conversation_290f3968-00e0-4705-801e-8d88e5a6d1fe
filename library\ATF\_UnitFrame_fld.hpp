// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_base_fld.hpp>


START_ATF_NAMESPACE
    struct  _UnitFrame_fld : _base_fld
    {
        int m_nFRAType;
        int m_nUnit_HP;
        float m_fDefGap;
        float m_fDefFacing;
        float m_fMoveRate_Seed;
        float m_fHeight;
        float m_fWidth;
        int m_nMoney;
        int m_nStdPrice;
        int m_nStdPoint;
        int m_nGoldPoint;
        int m_nKillPoint;
        int m_nProcPoint;
        int m_nStoragePrice;
        int m_nRepPrice;
        int m_bDestroy;
        int m_bRepair;
        char m_strDFHead[64];
        char m_strDFUpper[64];
        char m_strDFLower[64];
        char m_strDFArms[64];
        char m_strDFShoulder[64];
        char m_strDFBack[64];
        char m_strDFBtA[64];
        char m_strDFBtS[64];
    };
END_ATF_NAMESPACE
