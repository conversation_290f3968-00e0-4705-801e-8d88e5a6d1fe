// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace vc_attributes
    {
        template<>
        struct pointer_defaultAttribute
        {
            template<>
            enum type_e
            {
                ptr = 0x0,
                ref = 0x1,
                unique = 0x2,
            };
            type_e type;
        };
    }; // end namespace vc_attributes
END_ATF_NAMESPACE
