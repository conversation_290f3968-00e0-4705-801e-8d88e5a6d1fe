// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_INVENKEY.hpp>
#include <_STORAGE_LIST.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _TRUNK_DB_BASE
    {
        struct  _LIST
        {
            _INVENKEY Key;
            unsigned __int64 dwDur;
            unsigned int dwUpt;
            char byRace;
            unsigned int dwItemETSerial;
            unsigned __int64 lnUID;
            char byCsMethod;
            unsigned int dwT;
            unsigned int dwLendRegdTime;
        public:
            void Init();
            bool Release();
            bool Set(_STORAGE_LIST::_db_con* pItem, char byRaceCode);
            _LIST();
            void ctor__LIST();
        };
        char wszPasswd[13];
        long double dDalant;
        long double dGold;
        char byHintIndex;
        char wszHintAnswer[17];
        char by<PERSON><PERSON><PERSON><PERSON>;
        _LIST m_List[100];
        char byExtSlotNum;
        _LIST m_ExtList[40];
    public:
        void Init();
        _TRUNK_DB_BASE();
        void ctor__TRUNK_DB_BASE();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_TRUNK_DB_BASE, 5369>(), "_TRUNK_DB_BASE");
END_ATF_NAMESPACE
