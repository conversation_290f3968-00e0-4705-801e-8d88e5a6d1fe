// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_base_fld.hpp>


START_ATF_NAMESPACE
    struct  _UnitPart_fld : _base_fld
    {
        char m_strModle[64];
        int m_nIconIDX;
        char m_strName[64];
        int m_nFixPart;
        char m_strDefFrame[64];
        int m_nWPType;
        int m_nEffectGroup;
        int m_nNeedBt;
        int m_bAbr;
        int m_nDurUnit;
        int m_nLevelLim;
        int m_nUpLevelLim;
        int m_nExpertID1;
        int m_nExpertLim1;
        int m_nExpertID2;
        int m_nExpertLim2;
        float m_fAttGap;
        int m_nAttack_DP;
        float m_fAttackRange;
        int m_nAttackDel;
        float m_fMoveSpdRev;
        int m_nGAMinAF;
        int m_nGAMaxAF;
        int m_nAttMastery;
        int m_nGAMinSelProb;
        int m_nGAMaxSelProb;
        int m_nDefFc;
        int m_nDefMastery;
        int m_nProperty;
        int m_nFireTol;
        int m_nWaterTol;
        int m_nSoilTol;
        int m_nWindTol;
        int m_nMoney;
        int m_nStdPrice;
        int m_nStdPoint;
        int m_nRepPrice;
        int m_nDesrepPrice;
        int m_nBstCha;
        float m_fBstSpd;
        int m_nBackSlt;
        int m_nEff1Code;
        float m_fEff1Unit;
        int m_nEff2Code;
        float m_fEff2Unit;
        int m_nEff3Code;
        float m_fEff3Unit;
        int m_nEff4Code;
        float m_fEff4Unit;
        char m_strTooltipIndex[64];
        int m_nAttEffType;
        int m_nDefEffType;
    };
END_ATF_NAMESPACE
