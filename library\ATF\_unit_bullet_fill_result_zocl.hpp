// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _unit_bullet_fill_result_zocl
    {
        char byRetCode;
        char bySlotIndex;
        unsigned __int16 wBulletIndex[2];
        unsigned int dwComsumMoney[7];
        unsigned int dwLeftMoney[7];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE
