// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>



START_ATF_NAMESPACE
    struct _ZONEATTRIBUTES
    {
        unsigned int cbSize;
        wchar_t szDisplayName[260];
        wchar_t szDescription[200];
        wchar_t szIconPath[260];
        unsigned int dwTemplateMinLevel;
        unsigned int dwTemplateRecommended;
        unsigned int dwTemplateCurrentLevel;
        unsigned int dwFlags;
    };
END_ATF_NAMESPACE
