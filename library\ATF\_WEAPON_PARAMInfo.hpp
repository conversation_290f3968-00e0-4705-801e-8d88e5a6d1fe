// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_WEAPON_PARAM.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _WEAPON_PARAMFixUnit2_ptr = void (WINAPIV*)(struct _WEAPON_PARAM*, struct _UNIT_DB_BASE::_LIST*);
        using _WEAPON_PARAMFixUnit2_clbk = void (WINAPIV*)(struct _WEAPON_PARAM*, struct _UNIT_DB_BASE::_LIST*, _WEAPON_PARAMFixUnit2_ptr);
        using _WEAPON_PARAMFixWeapon4_ptr = void (WINAPIV*)(struct _WEAPON_PARAM*, struct _STORAGE_LIST::_db_con*);
        using _WEAPON_PARAMFixWeapon4_clbk = void (WINAPIV*)(struct _WEAPON_PARAM*, struct _STORAGE_LIST::_db_con*, _WEAPON_PARAMFixWeapon4_ptr);
        using _WEAPON_PARAMGetAttackDelay6_ptr = unsigned int (WINAPIV*)(struct _WEAPON_PARAM*, int, int);
        using _WEAPON_PARAMGetAttackDelay6_clbk = unsigned int (WINAPIV*)(struct _WEAPON_PARAM*, int, int, _WEAPON_PARAMGetAttackDelay6_ptr);
        using _WEAPON_PARAMGetAttackToolType8_ptr = int (WINAPIV*)(struct _WEAPON_PARAM*);
        using _WEAPON_PARAMGetAttackToolType8_clbk = int (WINAPIV*)(struct _WEAPON_PARAM*, _WEAPON_PARAMGetAttackToolType8_ptr);
        using _WEAPON_PARAMGetWeaponTolType10_ptr = int (WINAPIV*)(struct _WEAPON_PARAM*, struct _STORAGE_LIST::_db_con*);
        using _WEAPON_PARAMGetWeaponTolType10_clbk = int (WINAPIV*)(struct _WEAPON_PARAM*, struct _STORAGE_LIST::_db_con*, _WEAPON_PARAMGetWeaponTolType10_ptr);
        using _WEAPON_PARAMInit12_ptr = void (WINAPIV*)(struct _WEAPON_PARAM*);
        using _WEAPON_PARAMInit12_clbk = void (WINAPIV*)(struct _WEAPON_PARAM*, _WEAPON_PARAMInit12_ptr);
        using _WEAPON_PARAMSetStaticMember14_ptr = void (WINAPIV*)(struct CRecordData*);
        using _WEAPON_PARAMSetStaticMember14_clbk = void (WINAPIV*)(struct CRecordData*, _WEAPON_PARAMSetStaticMember14_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE
