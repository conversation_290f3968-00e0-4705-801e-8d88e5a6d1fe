// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _unmannedtrader_page_info
    {
        struct __list
        {
            unsigned int dwSerial;
            unsigned int dwK;
            unsigned __int64 dwD;
            unsigned int dwU;
            unsigned int dwPrice;
            unsigned int dwOwner;
            char wszOwnerName[17];
            __int64 tRegdate;
            char bySellturm;
            unsigned int dwT;
            unsigned __int64 lnUID;
        };
        unsigned int dwCnt;
        __list list[10];
    };
END_ATF_NAMESPACE
