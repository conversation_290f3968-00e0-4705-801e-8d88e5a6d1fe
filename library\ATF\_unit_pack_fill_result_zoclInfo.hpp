// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_unit_pack_fill_result_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _unit_pack_fill_result_zoclctor__unit_pack_fill_result_zocl2_ptr = void (WINAPIV*)(struct _unit_pack_fill_result_zocl*);
        using _unit_pack_fill_result_zoclctor__unit_pack_fill_result_zocl2_clbk = void (WINAPIV*)(struct _unit_pack_fill_result_zocl*, _unit_pack_fill_result_zoclctor__unit_pack_fill_result_zocl2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE
