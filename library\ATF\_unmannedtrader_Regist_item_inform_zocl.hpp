// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _unmannedtrader_Regist_item_inform_zocl
    {
        struct __list
        {
            bool bUnknown;
            unsigned __int16 wItemSerial;
            unsigned int dwRegistSerial;
            unsigned int dwPrice;
            unsigned int dwLeftSec;
            unsigned int dwListIndex;
        };
        char by<PERSON>um;
        __list List[10];
    public:
        _unmannedtrader_Regist_item_inform_zocl();
        void ctor__unmannedtrader_Regist_item_inform_zocl();
        int size();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_unmannedtrader_Regist_item_inform_zocl, 191>(), "_unmannedtrader_Regist_item_inform_zocl");
END_ATF_NAMESPACE
