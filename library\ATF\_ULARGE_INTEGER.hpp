// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$B950AFB169DC87688B328897744C612F.hpp>


START_ATF_NAMESPACE
    union _ULARGE_INTEGER
    {
        $B950AFB169DC87688B328897744C612F __s0;
        $B950AFB169DC87688B328897744C612F u;
        unsigned __int64 QuadPart;
    };
END_ATF_NAMESPACE
