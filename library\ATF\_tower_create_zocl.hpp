// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _tower_create_zocl
    {
        unsigned __int16 wIndex;
        unsigned __int16 wRecIndex;
        unsigned int dwSerial;
        __int16 zPos[3];
        unsigned int dwMasterSerial;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE
