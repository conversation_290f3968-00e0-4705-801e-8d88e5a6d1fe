// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct _worlddb_userinterface_info
    {
        unsigned __int16 wLinkBoard[50];
        unsigned int dwDamageCForce[8];
        unsigned int dwHelpCForce[8];
        unsigned int dwSkill[2];
        unsigned int dwForce[2];
        unsigned int dwCharacter[2];
        unsigned int dwAnimus[2];
        unsigned int dwInven;
        unsigned int dwInvenBag[5];
        char byLinkBoardLock;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE
