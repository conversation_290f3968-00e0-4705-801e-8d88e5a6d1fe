// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <__TEMP_WAIT_TOWER.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using __TEMP_WAIT_TOWERctor___TEMP_WAIT_TOWER2_ptr = void (WINAPIV*)(struct __TEMP_WAIT_TOWER*);
        using __TEMP_WAIT_TOWERctor___TEMP_WAIT_TOWER2_clbk = void (WINAPIV*)(struct __TEMP_WAIT_TOWER*, __TEMP_WAIT_TOWERctor___TEMP_WAIT_TOWER2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE
