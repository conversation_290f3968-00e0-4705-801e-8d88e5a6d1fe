// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_TRADE_DB_BASE.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _TRADE_DB_BASEClear2_ptr = void (WINAPIV*)(struct _TRADE_DB_BASE*);
        using _TRADE_DB_BASEClear2_clbk = void (WINAPIV*)(struct _TRADE_DB_BASE*, _TRADE_DB_BASEClear2_ptr);
        using _TRADE_DB_BASEInit4_ptr = void (WINAPIV*)(struct _TRADE_DB_BASE*);
        using _TRADE_DB_BASEInit4_clbk = void (WINAPIV*)(struct _TRADE_DB_BASE*, _TRADE_DB_BASEInit4_ptr);
        
        using _TRADE_DB_BASEctor__TRADE_DB_BASE6_ptr = void (WINAPIV*)(struct _TRADE_DB_BASE*);
        using _TRADE_DB_BASEctor__TRADE_DB_BASE6_clbk = void (WINAPIV*)(struct _TRADE_DB_BASE*, _TRADE_DB_BASEctor__TRADE_DB_BASE6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE
