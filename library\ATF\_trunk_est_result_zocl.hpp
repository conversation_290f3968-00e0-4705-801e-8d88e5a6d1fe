// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _trunk_est_result_zocl
    {
        char byRetCode;
        unsigned int dwLeftDalant;
        char szPW[16];
    public:
        _trunk_est_result_zocl();
        void ctor__trunk_est_result_zocl();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_trunk_est_result_zocl, 21>(), "_trunk_est_result_zocl");
END_ATF_NAMESPACE
