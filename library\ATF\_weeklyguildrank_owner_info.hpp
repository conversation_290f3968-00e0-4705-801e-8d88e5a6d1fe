// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _weeklyguildrank_owner_info
    {
        struct _list
        {
            unsigned int dwSerial;
            char wszGuildName[17];
            char byRace;
            unsigned __int16 wRank;
            char byGrade;
            long double dKillPvpPoint;
            long double dGuildBattlePvpPoint;
            unsigned int dwSumLv;
            unsigned int dwSumRankScore;
        };
        unsigned __int16 wRaceCnt[4];
        unsigned __int16 wCount;
        _list list[6];
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_weeklyguildrank_owner_info, 352>(), "_weeklyguildrank_owner_info");
END_ATF_NAMESPACE
