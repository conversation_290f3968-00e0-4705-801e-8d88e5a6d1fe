// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _unit_sell_result_zocl
    {
        char byRetCode;
        char bySlotIndex;
        unsigned __int16 wKeySerial;
        unsigned int dwNonPayDalant;
        int nAddMoney[7];
        unsigned int dwLeftMoney[7];
    };
END_ATF_NAMESPACE
