// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_base_fld.hpp>
#include <_react_obj.hpp>


START_ATF_NAMESPACE
    struct __add_loot_item
    {
        char byItemTableCode;
        _base_fld *pItemFld;
        unsigned int dwDur;
        _react_obj ReactObj;
    public:
        __add_loot_item();
        void ctor___add_loot_item();
    };
END_ATF_NAMESPACE
