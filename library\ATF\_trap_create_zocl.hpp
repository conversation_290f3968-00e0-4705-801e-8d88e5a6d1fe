// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _trap_create_zocl
    {
        unsigned __int16 wRecIndex;
        unsigned __int16 wIndex;
        unsigned int dwSerial;
        __int16 zPos[3];
        unsigned int dwMasterSerial;
        char byRaceCode;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE
