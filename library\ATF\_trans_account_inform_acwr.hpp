// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_GLBID.hpp>
#include <_SYSTEMTIME.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _trans_account_inform_acwr
    {
        _GLBID gidGlobal;
        unsigned int dwKey[4];
        unsigned int dwClientIP;
        unsigned int dwAccountSerial;
        char szAccountID[13];
        char byUserDgr;
        char bySubDgr;
        bool bChatLock;
        __int16 iType;
        char szCMS[7];
        int lRemainTime;
        _SYSTEMTIME stEndDate;
        char byUILock;
        char byUILock_failcnt;
        char szUILock_pw[13];
        char szAccount_pw[13];
        char byUILock_HintIndex;
        char uszUILock_HintAnswer[17];
        char byUILockFindPassFailCount;
        unsigned int dwRequestMoveCharacterSerialList[3];
        unsigned int dwTournamentCharacterSerialList[3];
        bool bIsPcBang;
        int nTrans;
        bool bAgeLimit;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE
