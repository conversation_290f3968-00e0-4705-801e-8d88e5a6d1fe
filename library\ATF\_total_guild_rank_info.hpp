// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _total_guild_rank_info
    {
        struct _list
        {
            unsigned __int16 wRank;
            unsigned int dwSerial;
            long double dPowerPoint;
            char wszGuildName[17];
            char byRace;
            char byGrade;
            unsigned int dwMasterSerial;
            char wszMasterName[17];
        };
        unsigned __int16 wRaceCnt[4];
        unsigned __int16 wCount;
        _list list[500];
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_total_guild_rank_info, 32016>(), "_total_guild_rank_info");
END_ATF_NAMESPACE
