// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _trunk_io_merge_request_clzo
    {
        char byStartStorageIndex;
        char byTarStorageIndex;
        unsigned __int16 wStartItemSerial;
        unsigned __int16 wTarItemSerial;
        unsigned __int16 wMoveAmount;
    };
END_ATF_NAMESPACE
