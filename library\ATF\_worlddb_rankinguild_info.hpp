// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _worlddb_rankinguild_info
    {
        struct __worlddb_rankinguild_data
        {
            unsigned int dwSerial;
            char byGrade;
            char byLv;
            unsigned int dwPvpPoint;
        };
        unsigned __int16 wRecordCount;
        __worlddb_rankinguild_data MemberData[50];
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_worlddb_rankinguild_info, 604>(), "_worlddb_rankinguild_info");
END_ATF_NAMESPACE
