// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace vc_attributes
    {
        #pragma pack(push, 4)
        template<>
        struct event_receiverAttribute
        {
            template<>
            enum type_e
            {
                native = 0x0,
                com = 0x1,
                managed = 0x2,
            };
            type_e type;
            bool layout_dependent;
        };
        #pragma pack(pop)
    }; // end namespace vc_attributes
END_ATF_NAMESPACE
