// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _worlddb_user_count_info
    {
        struct __user_count_info
        {
            unsigned __int16 wYear;
            char byMonth;
            char byDay;
            char byHour;
            unsigned int dwAvgCount;
            unsigned int dwMaxCount;
        };
        unsigned __int16 wRowCount;
        __user_count_info UserCount[256];
    };
END_ATF_NAMESPACE
