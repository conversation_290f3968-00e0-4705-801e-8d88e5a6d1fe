// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _unmannedtrader_buy_item_request_clzo
    {
        struct __list
        {
            unsigned int dwRegistSerial;
            unsigned int dwPrice;
        };
        char byDivision;
        char byClass;
        char bySubClass;
        unsigned int dwVer;
        char byStoreIndex;
        char byNum;
        __list List[10];
        int bUseNpcLink;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE
