// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace vc_attributes
    {
        namespace atl
        {
            template<>
            struct perf_counterAttribute
            {
                const char *namestring;
                const char *helpstring;
                int name_res;
                int help_res;
                int countertype;
                int defscale;
                bool default_counter;
                int detail;
                int max_counter_size;
                const char *countertype_string;
            };
        }; // end namespace atl
    }; // end namespace vc_attributes
END_ATF_NAMESPACE
