// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_GUID.hpp>



START_ATF_NAMESPACE
    struct _WSANAMESPACE_INFOW
    {
        _GUID NSProviderId;
        unsigned int dwNameSpace;
        int fActive;
        unsigned int dwVersion;
        wchar_t *lpszIdentifier;
    };
END_ATF_NAMESPACE
