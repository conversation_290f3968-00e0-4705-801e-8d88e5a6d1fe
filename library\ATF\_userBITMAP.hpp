// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct _userBITMAP
    {
        int bmType;
        int bmWidth;
        int bmHeight;
        int bmWidthBytes;
        unsigned __int16 bmPlanes;
        unsigned __int16 bmBitsPixel;
        unsigned int cbSize;
        char pBuffer[1];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE
