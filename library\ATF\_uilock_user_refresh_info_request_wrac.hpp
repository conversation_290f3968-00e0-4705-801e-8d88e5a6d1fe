// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_GLBID.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _uilock_user_refresh_info_request_wrac
    {
        unsigned int dwAccountSerial;
        char byFailCnt;
        char byFindPassFailCount;
        _GLBID gidGlobal;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE
