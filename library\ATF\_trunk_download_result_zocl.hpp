// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _trunk_download_result_zocl
    {
        struct _list
        {
            unsigned __int16 wSerial;
            char byTableCode;
             unsigned __int16 wItemIndex;
             unsigned __int64 dwDurPoint;
            char byClientIndex;
             unsigned int dwUptInfo;
            char byRace;
            char byCsMethod;
            unsigned int dwT;
        };
        char byRetCode;
        long double dDalant;
        long double dGold;
        char byListNum;
        char byPackNum;
        char byExtListNum;
        char byExtPackNum;
        _list ExtList[40];
        _list List[100];
    public:
        _trunk_download_result_zocl();
        void ctor__trunk_download_result_zocl();
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_trunk_download_result_zocl, 3381>(), "_trunk_download_result_zocl");
END_ATF_NAMESPACE
