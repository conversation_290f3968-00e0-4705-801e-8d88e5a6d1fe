// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_base_fld.hpp>
#include <_react_area.hpp>
#include <_react_obj.hpp>


START_ATF_NAMESPACE
    struct __respond_check
    {
        char byActTableCode;
        _base_fld *pActObjFld;
        _react_obj ReactObj;
        _react_area ReactArea;
        char *pszMsg;
    public:
        __respond_check();
        void ctor___respond_check();
        ~__respond_check();
        void dtor___respond_check();
    };
END_ATF_NAMESPACE
