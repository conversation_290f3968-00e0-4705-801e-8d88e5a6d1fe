// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_WAIT_ENTER_ACCOUNT.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _WAIT_ENTER_ACCOUNTRelease2_ptr = void (WINAPIV*)(struct _WAIT_ENTER_ACCOUNT*);
        using _WAIT_ENTER_ACCOUNTRelease2_clbk = void (WINAPIV*)(struct _WAIT_ENTER_ACCOUNT*, _WAIT_ENTER_ACCOUNTRelease2_ptr);
        using _WAIT_ENTER_ACCOUNTSetAgeLimitFlag4_ptr = void (WINAPIV*)(struct _WAIT_ENTER_ACCOUNT*, bool);
        using _WAIT_ENTER_ACCOUNTSetAgeLimitFlag4_clbk = void (WINAPIV*)(struct _WAIT_ENTER_ACCOUNT*, bool, _WAIT_ENTER_ACCOUNTSetAgeLimitFlag4_ptr);
        using _WAIT_ENTER_ACCOUNTSetBillingInfo6_ptr = void (WINAPIV*)(struct _WAIT_ENTER_ACCOUNT*, int16_t, char*, int, struct _SYSTEMTIME*);
        using _WAIT_ENTER_ACCOUNTSetBillingInfo6_clbk = void (WINAPIV*)(struct _WAIT_ENTER_ACCOUNT*, int16_t, char*, int, struct _SYSTEMTIME*, _WAIT_ENTER_ACCOUNTSetBillingInfo6_ptr);
        using _WAIT_ENTER_ACCOUNTSetData8_ptr = void (WINAPIV*)(struct _WAIT_ENTER_ACCOUNT*, unsigned int, char*, char, char, struct _GLBID*, unsigned int*, bool);
        using _WAIT_ENTER_ACCOUNTSetData8_clbk = void (WINAPIV*)(struct _WAIT_ENTER_ACCOUNT*, unsigned int, char*, char, char, struct _GLBID*, unsigned int*, bool, _WAIT_ENTER_ACCOUNTSetData8_ptr);
        using _WAIT_ENTER_ACCOUNTSetPcBangFlag10_ptr = void (WINAPIV*)(struct _WAIT_ENTER_ACCOUNT*, bool);
        using _WAIT_ENTER_ACCOUNTSetPcBangFlag10_clbk = void (WINAPIV*)(struct _WAIT_ENTER_ACCOUNT*, bool, _WAIT_ENTER_ACCOUNTSetPcBangFlag10_ptr);
        using _WAIT_ENTER_ACCOUNTSetTransFlag12_ptr = void (WINAPIV*)(struct _WAIT_ENTER_ACCOUNT*, int);
        using _WAIT_ENTER_ACCOUNTSetTransFlag12_clbk = void (WINAPIV*)(struct _WAIT_ENTER_ACCOUNT*, int, _WAIT_ENTER_ACCOUNTSetTransFlag12_ptr);
        using _WAIT_ENTER_ACCOUNTSetUILock14_ptr = void (WINAPIV*)(struct _WAIT_ENTER_ACCOUNT*, char, char*, char, char*, char, char*, char);
        using _WAIT_ENTER_ACCOUNTSetUILock14_clbk = void (WINAPIV*)(struct _WAIT_ENTER_ACCOUNT*, char, char*, char, char*, char, char*, char, _WAIT_ENTER_ACCOUNTSetUILock14_ptr);
        
        using _WAIT_ENTER_ACCOUNTctor__WAIT_ENTER_ACCOUNT16_ptr = void (WINAPIV*)(struct _WAIT_ENTER_ACCOUNT*);
        using _WAIT_ENTER_ACCOUNTctor__WAIT_ENTER_ACCOUNT16_clbk = void (WINAPIV*)(struct _WAIT_ENTER_ACCOUNT*, _WAIT_ENTER_ACCOUNTctor__WAIT_ENTER_ACCOUNT16_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE
