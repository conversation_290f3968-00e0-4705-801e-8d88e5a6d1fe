// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _trap_fixpositon_zocl
    {
        unsigned __int16 wRecIndex;
        unsigned __int16 wIndex;
        unsigned int dwSerial;
        __int16 zCur[3];
        unsigned __int16 wCompLeftSec;
        unsigned int dwMasterSerial;
        bool bTranspar;
        char byRaceCode;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE
