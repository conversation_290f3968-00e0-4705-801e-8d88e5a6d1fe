// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_STORAGE_LIST.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _TOWER_PARAM
    {
        struct _list
        {
            _STORAGE_LIST::_db_con *m_pTowerItem;
            unsigned __int16 m_wItemSerial;
            struct CGuardTower *m_pTowerObj;
        public:
            void init();
        };
        int m_nCount;
        _list m_List[6];
    public:
        void Init();
        bool IsEmpty();
        void NotifyOwnerAttackInform(struct CCharacter* pDst);
        bool PushList(struct _STORAGE_LIST::_db_con* pTowerItem, struct CGuardTower* pTowerObj);
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_TOWER_PARAM, 152>(), "_TOWER_PARAM");
END_ATF_NAMESPACE
