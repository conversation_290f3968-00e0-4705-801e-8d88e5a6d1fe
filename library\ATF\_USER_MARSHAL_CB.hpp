// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_MIDL_STUB_MESSAGE.hpp>


START_ATF_NAMESPACE
    struct _USER_MARSHAL_CB
    {
        unsigned int Flags;
        _MIDL_STUB_MESSAGE *pStubMsg;
        const char *pReserve;
        unsigned int Signature;
        _USER_MARSHAL_CB_TYPE CBType;
        const char *pFormat;
        const char *pTypeFormat;
    };
END_ATF_NAMESPACE
