// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_update_candidate_wincount_packing.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _update_candidate_wincount_packingctor__update_candidate_wincount_packing2_ptr = void (WINAPIV*)(struct _update_candidate_wincount_packing*);
        using _update_candidate_wincount_packingctor__update_candidate_wincount_packing2_clbk = void (WINAPIV*)(struct _update_candidate_wincount_packing*, _update_candidate_wincount_packingctor__update_candidate_wincount_packing2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE
