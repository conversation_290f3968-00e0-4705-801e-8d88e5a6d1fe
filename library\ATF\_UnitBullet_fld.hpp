// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_base_fld.hpp>


START_ATF_NAMESPACE
    struct  _UnitBullet_fld : _base_fld
    {
        char m_strModle[64];
        int m_nIconIDX;
        char m_strName[64];
        int m_nWPType;
        int m_bAbr;
        int m_nDurUnit;
        int m_nMoney;
        int m_nStdPrice;
        int m_nStdPoint;
        float m_fGAAF;
        char m_strEffectIndex[64];
        char m_strTooltipIndex[64];
    };
END_ATF_NAMESPACE
