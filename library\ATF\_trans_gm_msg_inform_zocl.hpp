// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _trans_gm_msg_inform_zocl
    {
        unsigned __int16 wMsgSize;
        char wszChatData[1281];
    public:
        _trans_gm_msg_inform_zocl();
        void ctor__trans_gm_msg_inform_zocl();
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_trans_gm_msg_inform_zocl, 1283>(), "_trans_gm_msg_inform_zocl");
END_ATF_NAMESPACE
