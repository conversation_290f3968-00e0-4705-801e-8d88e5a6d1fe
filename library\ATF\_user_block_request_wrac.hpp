// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_CLID.hpp>
#include <_GLBID.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _user_block_request_wrac
    {
        char byBlockType;
        int iPeriod;
        _GLBID gidGlobalForTarget;
        _CLID idLocalForGM;
        char uszReason[32];
        char uszWriter[32];
        unsigned int dwAccountSerial;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE
