// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _worlddb_potion_delay_info
    {
        unsigned int dwPotionDelay[38];
    public:
        _worlddb_potion_delay_info();
        void ctor__worlddb_potion_delay_info();
    };    
    static_assert(ATF::checkSize<_worlddb_potion_delay_info, 152>(), "_worlddb_potion_delay_info");
END_ATF_NAMESPACE
