// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _trunk_extend_result_zocl
    {
        char byRetCode;
        char byPackNum;
        unsigned int dwLeftDalant;
        unsigned int dwConsumDanlant;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE
