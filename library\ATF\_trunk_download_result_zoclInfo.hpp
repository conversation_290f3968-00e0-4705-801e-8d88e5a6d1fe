// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_trunk_download_result_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _trunk_download_result_zoclctor__trunk_download_result_zocl2_ptr = void (WINAPIV*)(struct _trunk_download_result_zocl*);
        using _trunk_download_result_zoclctor__trunk_download_result_zocl2_clbk = void (WINAPIV*)(struct _trunk_download_result_zocl*, _trunk_download_result_zoclctor__trunk_download_result_zocl2_ptr);
        using _trunk_download_result_zoclsize4_ptr = int (WINAPIV*)(struct _trunk_download_result_zocl*);
        using _trunk_download_result_zoclsize4_clbk = int (WINAPIV*)(struct _trunk_download_result_zocl*, _trunk_download_result_zoclsize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE
