// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>



START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _TRUSTEE_ACCESSW
    {
        wchar_t *lpProperty;
        unsigned int Access;
        unsigned int fAccessFlags;
        unsigned int fReturnedAccess;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE
