// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <__respond_check.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using __respond_checkctor___respond_check2_ptr = void (WINAPIV*)(struct __respond_check*);
        using __respond_checkctor___respond_check2_clbk = void (WINAPIV*)(struct __respond_check*, __respond_checkctor___respond_check2_ptr);
        
        using __respond_checkdtor___respond_check6_ptr = void (WINAPIV*)(struct __respond_check*);
        using __respond_checkdtor___respond_check6_clbk = void (WINAPIV*)(struct __respond_check*, __respond_checkdtor___respond_check6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE
