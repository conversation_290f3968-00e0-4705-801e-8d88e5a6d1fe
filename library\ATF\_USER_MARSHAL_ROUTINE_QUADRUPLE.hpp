// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _USER_MARSHAL_ROUTINE_QUADRUPLE
    {
        unsigned int (WINAPIV *pfnBufferSize)(unsigned int *, unsigned int, void *);
        char *(WINAPIV *pfnMarshall)(unsigned int *, char *, void *);
        char *(WINAPIV *pfnUnmarshall)(unsigned int *, char *, void *);
        void (WINAPIV *pfnFree)(unsigned int *, void *);
    };
END_ATF_NAMESPACE
