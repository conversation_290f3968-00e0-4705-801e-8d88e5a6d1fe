// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <__respawn_monster.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using __respawn_monsterctor___respawn_monster2_ptr = void (WINAPIV*)(struct __respawn_monster*);
        using __respawn_monsterctor___respawn_monster2_clbk = void (WINAPIV*)(struct __respawn_monster*, __respawn_monsterctor___respawn_monster2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE
