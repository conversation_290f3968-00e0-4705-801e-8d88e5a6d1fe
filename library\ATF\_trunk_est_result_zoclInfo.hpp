// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_trunk_est_result_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _trunk_est_result_zoclctor__trunk_est_result_zocl2_ptr = void (WINAPIV*)(struct _trunk_est_result_zocl*);
        using _trunk_est_result_zoclctor__trunk_est_result_zocl2_clbk = void (WINAPIV*)(struct _trunk_est_result_zocl*, _trunk_est_result_zoclctor__trunk_est_result_zocl2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE
