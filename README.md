# Yorozuya - RFOnline Server Security & Anti-Cheat System

[![License](https://img.shields.io/badge/license-Free-green.svg)](LICENSE)
[![Platform](https://img.shields.io/badge/platform-Windows%20x64-blue.svg)]()
[![RFOnline](https://img.shields.io/badge/RFOnline-v2.2.3.2-orange.svg)]()
[![Visual Studio](https://img.shields.io/badge/VS-2017%2B-purple.svg)]()

**Yorozuya** is a comprehensive, free, and open-source security and anti-cheat defense system specifically designed for **RFOnline server version 2.2.3.2**. It provides robust protection against exploits, duplication bugs, hacks, and various security vulnerabilities commonly found in RFOnline private servers.

## 🚀 Features

### Core Security Systems
- **Real-time exploit detection and prevention**
- **Memory protection and server hardening**
- **Modular architecture for easy customization**
- **Configuration-driven security policies**
- **Comprehensive logging and monitoring**

### Anti-Cheat Protection
- **Movement validation** (speed/fly/wall hack detection)
- **Combat integrity** (overdamage prevention)
- **Item duplication protection**
- **Economic system security** (auction, trade, mail)
- **PvP system integrity**

## 📋 Table of Contents

- [Installation](#-installation)
- [Build Requirements](#-build-requirements)
- [Architecture](#-architecture)
- [Security Fixes](#-security-fixes)
- [Addon System](#-addon-system)
- [Configuration](#-configuration)
- [Usage](#-usage)
- [Contributing](#-contributing)
- [License](#-license)

## 🛠 Build Requirements

### Prerequisites
- **Visual Studio 2017 15.5.2** or higher
- **x64 toolset** for Visual Studio
- **Windows SDK** (latest recommended)
- **Git** for version control

### Dependencies (Included)
- **ATF Framework** - Application Testing Framework
- **MinHook** - API hooking library
- **RapidJSON** - JSON parsing library
- **P7ClientLib** - Logging framework

## 🏗 Architecture

Yorozuya uses a sophisticated modular architecture designed for maximum flexibility and maintainability:

```
Yorozuya/
├── YorozuyaGS/          # Main game server module
│   ├── Player/          # Player-related security modules
│   ├── AttackSystem/    # Combat validation systems
│   ├── Guild/           # Guild system protection
│   ├── NetworkEx/       # Network security extensions
│   └── Common/          # Shared utilities and registry
├── Addons/              # Modular addon system
│   ├── GMCommands/      # Enhanced GM commands
│   ├── ChatLog/         # Chat logging system
│   ├── DefenceFormula/  # Custom defense calculations
│   └── [12+ more addons]
├── Common/              # Shared interfaces and helpers
├── library/             # Third-party dependencies
└── ClientRun/           # Client-side components
```

### Core Components

#### 1. **YorozuyaGS (Main Module)**
- **CYorozuya**: Main singleton controller
- **CModuleRegistry**: Dynamic module loading system
- **Player Systems**: Movement, equipment, trade validation
- **Combat Systems**: Damage calculation, buff management
- **Network Extensions**: Packet validation and filtering

#### 2. **Addon System**
Modular components that can be enabled/disabled:
- **AccuracyEffect**: Accuracy calculation modifications
- **BonusStart**: Starting bonus management
- **ChatLog**: Comprehensive chat logging
- **DefenceFormula**: Custom defense formulas
- **EnchantChance**: Enchantment probability control
- **GMCommands**: Enhanced GM command system
- **LootExchange**: Loot system modifications
- **MauExp**: Experience calculation adjustments
- **PvpPotion**: PvP potion management
- **RadiusDropLoot**: Loot drop radius control
- **ReplaceLootName**: Loot name customization
- **ServerMemoryPatch**: Low-level memory patches
- **StoneHP**: Stone HP display fixes
- **VariousSettings**: Miscellaneous server settings

## 🔒 Security Fixes

Yorozuya addresses **40+ critical vulnerabilities** and exploits:

### Duplication Exploits
- ✅ **Coordinate duplication** prevention
- ✅ **Item duplication** through trade system
- ✅ **Money duplication** protection
- ✅ **Quest item duplication** fixes
- ✅ **Box duplication** prevention

### Combat & Damage Exploits
- ✅ **Overdamage prevention** for melee attacks
- ✅ **Set item effect** validation
- ✅ **Double potion** exploit fixes
- ✅ **Potion removal race** condition fixes
- ✅ **Buff/debuff radius** control
- ✅ **Attack delay validation** (including magic spells)

### Movement & Teleportation Hacks
- ✅ **Speed hack** detection and prevention
- ✅ **Fly hack** protection
- ✅ **Wall hack** detection
- ✅ **Teleport to guild hall** fixes
- ✅ **Teleport to starting point** protection
- ✅ **Combat mode teleportation** prevention

### System Security
- ✅ **Auction system** vulnerability fixes
- ✅ **Mail system** security enhancements
- ✅ **Trade system** exploit prevention
- ✅ **Bank transfer** race condition fixes
- ✅ **SQL injection** protection
- ✅ **Server crash** prevention

### PvP & Guild Systems
- ✅ **PvP point overflow** (11+ players) fixes
- ✅ **Guild war PC overflow** prevention
- ✅ **Lossless PC transmission** fixes
- ✅ **Same race transmission** issues
- ✅ **Siege mode** buff/skill control

### Miscellaneous Fixes
- ✅ **GM command** security enhancements
- ✅ **Invisible character** creation prevention
- ✅ **Anti-invisible property** and mine detection
- ✅ **Premium item issuance** control
- ✅ **Voting system** improvements
- ✅ **Siege kit jumping** prevention
- ✅ **Eternal chip hunter effect** fixes
- ✅ **NpcData character rollback** protection
- ✅ **Turret and mine coordinate** validation

## ⚙ Configuration

Yorozuya uses JSON-based configuration for easy customization:

### Global Configuration (`YorozuyaGS/global.json`)
```json
{
  "intervals": {
    "open_world_wait": 5000,
    "step_delay": 100
  },
  "registry": {
    "modules": {
      "player_validation": true,
      "combat_protection": true,
      "movement_detection": true
    }
  }
}
```

### Module-Specific Configuration
Each addon can have its own configuration file for fine-tuned control.

## 🚀 Installation

### For Server Administrators

1. **Download** the latest release from the releases page
2. **Extract** the files to your RFOnline server directory
3. **Configure** the `global.json` file according to your needs
4. **Inject** the DLL into your server process:
   ```
   YorozuyaGS.dll -> Inject into RFOnline server process
   ```
5. **Verify** installation through server logs

### For Developers

1. **Clone** the repository:
   ```bash
   git clone https://github.com/your-repo/Yorozuya.git
   cd Yorozuya
   ```

2. **Open** `Yorozuya.sln` in Visual Studio 2017+

3. **Build** the solution:
   - Set configuration to `Release`
   - Set platform to `x64`
   - Build → Build Solution

4. **Deploy** the generated DLLs to your server

## 📖 Usage

### Basic Usage

1. **Start** your RFOnline server
2. **Inject** YorozuyaGS.dll into the server process
3. **Monitor** logs for security events
4. **Configure** modules as needed

### Advanced Configuration

#### Enabling/Disabling Modules
```json
{
  "registry": {
    "modules": {
      "GMCommands": true,
      "ChatLog": false,
      "PvpPotion": true
    }
  }
}
```

#### Custom Security Policies
Each module supports custom configuration for specific security policies and thresholds.

### Client-Side Patches

Some fixes require client-side patches. Apply these using a hex editor:

```
File offset | New Value | Description
---------------------------------
0x1415e6 | 90 B8 01 00 00 00 | For fix set items
0x144f8e | EB | For stone hp view
```

## 🔧 Development

### Adding New Modules

1. **Create** a new project in the `Addons` folder
2. **Implement** the `IModule` interface
3. **Register** your module with the registry
4. **Add** configuration support

Example module structure:
```cpp
class CMyModule : public IModule {
public:
    void load() override;
    void unload() override;
    void loop() override;
    void configure(const rapidjson::Value& config) override;
};
```

### Debugging

- Use **Visual Studio debugger** with the server process
- Enable **detailed logging** in configuration
- Monitor **P7 logs** for detailed information

## 🤝 Contributing

We welcome contributions! Please follow these guidelines:

1. **Fork** the repository
2. **Create** a feature branch
3. **Make** your changes with proper testing
4. **Submit** a pull request with detailed description

### Code Style
- Follow **existing code conventions**
- Use **meaningful variable names**
- Add **comprehensive comments**
- Include **error handling**

### Testing
- Test with **multiple server configurations**
- Verify **no performance impact**
- Ensure **compatibility** with existing systems

## 📄 License

This project is released under a **free license**. See [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Issues**: Report bugs and request features via GitHub Issues
- **Documentation**: Check the wiki for detailed documentation
- **Community**: Join our community forums for support

## 🙏 Acknowledgments

- **RFOnline Community** for identifying vulnerabilities
- **Contributors** who helped develop and test the system
- **Open Source Libraries** used in this project

## ⚠ Disclaimer

This software is provided "as is" without warranty. Use at your own risk. Always backup your server before implementing security changes.

---

**Made with ❤️ for the RFOnline community**
