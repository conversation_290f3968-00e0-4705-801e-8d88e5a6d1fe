// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_TOWER_PARAM.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _TOWER_PARAMInit2_ptr = void (WINAPIV*)(struct _TOWER_PARAM*);
        using _TOWER_PARAMInit2_clbk = void (WINAPIV*)(struct _TOWER_PARAM*, _TOWER_PARAMInit2_ptr);
        using _TOWER_PARAMIsEmpty4_ptr = bool (WINAPIV*)(struct _TOWER_PARAM*);
        using _TOWER_PARAMIsEmpty4_clbk = bool (WINAPIV*)(struct _TOWER_PARAM*, _TOWER_PARAMIsEmpty4_ptr);
        using _TOWER_PARAMNotifyOwnerAttackInform6_ptr = void (WINAPIV*)(struct _TOWER_PARAM*, struct CCharacter*);
        using _TOWER_PARAMNotifyOwnerAttackInform6_clbk = void (WINAPIV*)(struct _TOWER_PARAM*, struct CCharacter*, _TOWER_PARAMNotifyOwnerAttackInform6_ptr);
        using _TOWER_PARAMPushList8_ptr = bool (WINAPIV*)(struct _TOWER_PARAM*, struct _STORAGE_LIST::_db_con*, struct CGuardTower*);
        using _TOWER_PARAMPushList8_clbk = bool (WINAPIV*)(struct _TOWER_PARAM*, struct _STORAGE_LIST::_db_con*, struct CGuardTower*, _TOWER_PARAMPushList8_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE
