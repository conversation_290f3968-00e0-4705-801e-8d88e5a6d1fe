// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace vc_attributes
    {
        #pragma pack(push, 8)
        template<>
        struct idl_moduleAttribute
        {
            const char *name;
            const char *dllname;
            const char *version;
            const char *uuid;
            const char *helpstring;
            int helpstringcontext;
            int helpcontext;
            bool hidden;
            bool restricted;
        };
        #pragma pack(pop)
    }; // end namespace vc_attributes
END_ATF_NAMESPACE
