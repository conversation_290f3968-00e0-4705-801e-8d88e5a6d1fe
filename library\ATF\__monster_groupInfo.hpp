// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <__monster_group.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using __monster_groupctor___monster_group2_ptr = void (WINAPIV*)(struct __monster_group*);
        using __monster_groupctor___monster_group2_clbk = void (WINAPIV*)(struct __monster_group*, __monster_groupctor___monster_group2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE
