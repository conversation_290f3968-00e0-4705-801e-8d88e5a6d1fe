// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_UNWIND_CODE.hpp>


START_ATF_NAMESPACE
    struct _UNWIND_INFO
    {
        __int8 Version : 3;
        __int8 Flags : 5;
        char <PERSON>zeOfProlog;
        char CountOfCodes;
        __int8 FrameRegister : 4;
        __int8 FrameOffset : 4;
        _UNWIND_CODE UnwindCode[1];
    };
END_ATF_NAMESPACE
