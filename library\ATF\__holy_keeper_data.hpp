// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_dummy_position.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct __holy_keeper_data
    {
        struct CMapData *pCreateMap;
        _dummy_position CreateDummy;
        _dummy_position ActiveDummy;
        _dummy_position CenterDummy;
        struct _monster_fld *pRec;
    public:
        __holy_keeper_data();
        void ctor___holy_keeper_data();
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<__holy_keeper_data, 488>(), "__holy_keeper_data");
END_ATF_NAMESPACE
