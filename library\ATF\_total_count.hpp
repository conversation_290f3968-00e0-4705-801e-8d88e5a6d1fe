// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _total_count
    {
        unsigned int m_dwAcceptNum;
        unsigned int m_dwSendAbleNum;
        long double m_dTotalRecvBYTESize;
        long double m_dTotalSendBYTESize;
        unsigned int m_dwTotalRecvErrNum;
        unsigned int m_dwTotalSendErrNum;
        unsigned int m_dwTotalSendBlockNum;
        unsigned int m_dwTotalRecvBlockNum;
        unsigned int m_dwBufferClean;
        unsigned int m_dwFdWriteCnt;
        unsigned int m_dwNoFindEmptySocket;
        unsigned int m_dwNoEventSelect;
    public:
        _total_count();
        void ctor__total_count();
    };    
    static_assert(ATF::checkSize<_total_count, 56>(), "_total_count");
END_ATF_NAMESPACE
