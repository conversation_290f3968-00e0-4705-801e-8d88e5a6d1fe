// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct __MIDL___MIDL_itf_dimm_0000_0014
    {
        unsigned int dwPrivateDataSize;
        unsigned int fdwProperty;
        unsigned int fdwConversionCaps;
        unsigned int fdwSentenceCaps;
        unsigned int fdwUICaps;
        unsigned int fdwSCSCaps;
        unsigned int fdwSelectCaps;
    };
END_ATF_NAMESPACE
