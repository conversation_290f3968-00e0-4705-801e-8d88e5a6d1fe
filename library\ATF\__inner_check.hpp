// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_react_area.hpp>
#include <_react_obj.hpp>


START_ATF_NAMESPACE
    struct __inner_check
    {
        _react_area ReactArea_Evt;
        _react_area ReactArea_Aft;
        _react_obj ReactObj;
        char *pszMsg;
        char *pszRespawnCode;
    public:
        __inner_check();
        void ctor___inner_check();
        ~__inner_check();
        void dtor___inner_check();
    };
END_ATF_NAMESPACE
