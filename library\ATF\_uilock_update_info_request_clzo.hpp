// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _uilock_update_info_request_clzo
    {
        char uszUILockPW_Old[13];
        char usz<PERSON><PERSON>ockPW[13];
        char uszUILockPW_Confirm[13];
        char byHintIndex;
        char uszHintAnswer[17];
    };
END_ATF_NAMESPACE
