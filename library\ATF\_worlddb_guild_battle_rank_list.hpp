// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _worlddb_guild_battle_rank_list
    {
        struct __list
        {
            int nRank;
            char byGrade;
            char wszName[17];
            unsigned int dwWin;
            unsigned int dwLose;
            unsigned int dwDraw;
            unsigned int dwScore;
            unsigned int dwSerial;
        };
        unsigned __int16 wCount;
        __list list[500];
    };
END_ATF_NAMESPACE
