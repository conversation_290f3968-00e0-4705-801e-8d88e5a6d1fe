// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _UNIT_DB_BASE
    {
        struct  _LIST
        {
            char bySlotIndex;
            char byFrame;
            unsigned int dwGauge;
            char byPart[6];
            unsigned int dwBullet[2];
            unsigned int dwSpare[8];
            int nPullingFee;
            unsigned int dwCutTime;
            unsigned __int16 wBooster;
        public:
            void DelUnit();
            void Init(char byIndex);
            _LIST();
            void ctor__LIST();
        };
        _LIST m_List[4];
    public:
        void Init();
        _UNIT_DB_BASE();
        void ctor__UNIT_DB_BASE();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_UNIT_DB_BASE, 248>(), "_UNIT_DB_BASE");
END_ATF_NAMESPACE
