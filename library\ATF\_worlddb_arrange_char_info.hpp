// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_NOT_ARRANGED_AVATOR_DB.hpp>


START_ATF_NAMESPACE
    struct _worlddb_arrange_char_info
    {
        char byCount;
        _NOT_ARRANGED_AVATOR_DB ArrangeChar[50];
    public:
        _worlddb_arrange_char_info();
        void ctor__worlddb_arrange_char_info();
    };    
    static_assert(ATF::checkSize<_worlddb_arrange_char_info, 3451>(), "_worlddb_arrange_char_info");
END_ATF_NAMESPACE
