// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <CPlayer.hpp>
#include <_STORAGE_LIST.hpp>
#include <_character_create_setdata.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct  _tower_create_setdata : _character_create_setdata
    {
        int nHP;
        CPlayer *pMaster;
        char byRaceCode;
        _STORAGE_LIST::_db_con *pItem;
        int nIniIndex;
        bool bQuick;
    public:
        _tower_create_setdata();
        void ctor__tower_create_setdata();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_tower_create_setdata, 72>(), "_tower_create_setdata");
END_ATF_NAMESPACE
