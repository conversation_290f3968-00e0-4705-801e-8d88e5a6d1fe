// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$91811B9517CA7118F6226312FD12A3B3.hpp>


START_ATF_NAMESPACE
    struct _TRANSMIT_PACKETS_ELEMENT
    {
        unsigned int dwElFlags;
        unsigned int cLength;
        $91811B9517CA7118F6226312FD12A3B3 ___u2;
        void *hFile;
    };
END_ATF_NAMESPACE
